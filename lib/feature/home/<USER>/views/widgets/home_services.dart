import 'package:flutter/material.dart';
import 'package:gather_point/core/utils/app_assets.dart';
import 'package:gather_point/feature/reels/presentation/views/reels_page.dart';

class HomeServices extends StatelessWidget {
  const HomeServices({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 24),
      child: Column(
        children: [
          Row(
            children: [
              customHomeService(context, image: AppAssets.imagesHomeService),
            ],
          ),
        ],
      ),
    );
  }

  Widget customHomeService(BuildContext context, {required String image}) {
    return Expanded(
        child: GestureDetector(
            onTap: () {
              // Use post-frame callback for smooth navigation
              WidgetsBinding.instance.addPostFrameCallback((_) {
                if (context.mounted) {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                        builder: (context) => const ReelsPage(
                              searchResults: [],
                              searchQuery: '',
                              serviceCategoryId: 0,
                              selectedCity: null, // Generic service, no city filtering needed
                            )),
                  );
                }
              });
            },
            child: AspectRatio(
              aspectRatio: 166 / 223,
              child: ClipRRect(
                borderRadius: BorderRadius.circular(19),
                child: Image.asset(image, fit: BoxFit.cover),
              ),
            )));
  }
}
