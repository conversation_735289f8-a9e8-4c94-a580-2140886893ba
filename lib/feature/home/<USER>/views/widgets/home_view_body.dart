import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:gather_point/feature/home/<USER>/Data%20Sources/Models/City.dart';
import 'package:gather_point/feature/home/<USER>/Data%20Sources/Models/ServiceCategory.dart';
import 'package:gather_point/feature/home/<USER>/cubit/home_cubit.dart';
import 'package:gather_point/feature/home/<USER>/views/widgets/custom_home_title.dart';
import 'package:gather_point/feature/home/<USER>/views/widgets/home_search_section.dart';
import 'package:gather_point/feature/reels/presentation/views/reels_page.dart';

/// @deprecated This widget is deprecated. Use GatherPointHome from home_screen.dart instead.
/// This widget uses old direct API calls and doesn't follow the cubit pattern.

class HomeViewBody extends StatefulWidget {
  final City? currentCity;

  const HomeViewBody({super.key, required this.currentCity});

  @override
  State<HomeViewBody> createState() => _HomeViewBodyState();
}

class _HomeViewBodyState extends State<HomeViewBody> {
  final Dio _dio = Dio();
  late Future<List<ServiceCategory>> _categoriesFuture;

  @override
  void initState() {
    super.initState();
    _categoriesFuture = _fetchCategories();
  }

  Future<List<ServiceCategory>> _fetchCategories() async {
    try {
      final response = await _dio.get(
        'https://backend.gatherpoint.sa/api/service_categories/list',
        options: Options(responseType: ResponseType.json),
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = response.data['data'];
        return data.map((json) => ServiceCategory.fromJson(json)).toList();
      }
      throw Exception('Failed to load categories: ${response.statusCode}');
    } catch (e) {
      throw Exception('API Error: ${e.toString()}');
    }
  }

  void _refreshCategories() {
    setState(() {
      _categoriesFuture = _fetchCategories();
    });
  }

  @override
  Widget build(BuildContext context) {
    return RefreshIndicator(
      onRefresh: () async => _refreshCategories(),
      child: SingleChildScrollView(
        child: Column(
          children: [
            const HomeSearchSection(),
            const SizedBox(height: 48),
            const CustomHomeTitle(title: 'خدماتنا'),
            const SizedBox(height: 22),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24),
              child: FutureBuilder<List<ServiceCategory>>(
                future: _categoriesFuture,
                builder: (context, snapshot) {
                  if (snapshot.connectionState == ConnectionState.waiting) {
                    return const Center(
                      child: CircularProgressIndicator(),
                    );
                  }

                  if (snapshot.hasError) {
                    return Center(
                      child: Column(
                        children: [
                          Text('خطأ: ${snapshot.error}'),
                          const SizedBox(height: 20),
                          ElevatedButton(
                            onPressed: _refreshCategories,
                            child: const Text('إعادة المحاولة'),
                          ),
                        ],
                      ),
                    );
                  }

                  final categories = snapshot.data!;

                  return GridView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 1,
                      crossAxisSpacing: 16,
                      mainAxisSpacing: 16,
                      childAspectRatio: 166 / 223,
                    ),
                    itemCount: categories.length,
                    itemBuilder: (context, index) =>
                        _buildCategoryItem(categories[index]),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCategoryItem(ServiceCategory category) {
    return GestureDetector(
      onTap: () {
        // Use post-frame callback for smooth navigation
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (context.mounted) {
            // Get the selected city from HomeCubit
            City? selectedCity;
            try {
              final homeState = context.read<HomeCubit>().state;
              if (homeState is HomeLoaded) {
                selectedCity = homeState.currentCity;
              }
            } catch (e) {
              debugPrint('Error getting selected city from HomeCubit: $e');
            }

            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => ReelsPage(
                  searchResults: const [],
                  searchQuery: '',
                  serviceCategoryId: category.id,
                  selectedCity: selectedCity,
                ),/*CategoryView(
                  serviceCategoryId: category.id,
                  categoryTitle: category.title,
                ),*/
              ),
            );
          }
        });
      },
      child: ClipRRect(
        borderRadius: BorderRadius.circular(19),
        child: Stack(
          fit: StackFit.expand,
          children: [
            CachedNetworkImage(
              imageUrl: category.image,
              fit: BoxFit.cover,
              placeholder: (context, url) => Container(
                color: Colors.grey[200],
                child: const Center(child: CircularProgressIndicator()),
              ),
              errorWidget: (context, url, error) => const Icon(Icons.error),
            ),
            Positioned(
              bottom: 16,
              left: 16,
              right: 16,
              child: Text(
                category.title,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  shadows: [
                    Shadow(
                      offset: Offset(1, 1),
                      blurRadius: 3,
                      color: Colors.black54,
                    )
                  ],
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ],
        ),
      ),
    );
  }
}